'use client';

import { useSearchParams, usePathname } from 'next/navigation';
import { useAppConfigStore } from './app-config';
import { setCookie, getCookie } from 'cookies-next/client';
import { useEffect } from 'react';

export function useTagWithUrlSupport() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { updateConfig, config, isHydrated } = useAppConfigStore();

  // Get tag from URL
  const currentTag = searchParams?.get('shop') as 'man' | 'woman' | 'brands' | null;

  // Synchronize cookies with URL state
  useEffect(() => {
    if (!isHydrated || !currentTag) return;

    const isCollectionPage = pathname === '/collection';
    if (currentTag !== 'brands') {
      const currentCookie = getCookie('X-User-Pref');
      if (currentCookie !== currentTag) {
        setCookie('X-User-Pref', currentTag);
      }
    }
    if (isCollectionPage) {
      const brandSelectedCookie = getCookie('X-Is-Brand-Selected');
      const expectedValue = currentTag === 'brands' ? 'true' : 'false';

      if (brandSelectedCookie !== expectedValue) {
        setCookie('X-Is-Brand-Selected', expectedValue);
      }
    }
  }, [isHydrated, currentTag, pathname]);

  const setTag = (tag: 'man' | 'woman' | 'brands') => {
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('shop', tag);
    window.history.pushState({ path: newUrl.href }, '', newUrl.href);
    if (tag !== 'brands') {
      updateConfig({ userPreference: tag });
    }
  };

  return {
    tag: currentTag,
    setTag,
    userPreference: config.userPreference || 'man',
  };
}
